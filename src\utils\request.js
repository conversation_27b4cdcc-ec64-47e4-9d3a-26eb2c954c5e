import axios from "axios";
import Cookies from '@/utils/cookies';
import jwt_decode from "jwt-decode"
import { jsBridge } from '@/utils/jsBridge';
import { showFailToast, setToastDefaultOptions } from 'vant';
setToastDefaultOptions({iconSize: '0px'})
import i18n from "@/i18n";
const instance = axios.create({
  baseURL: process.env.VUE_APP_SERVER_URL,
  timeout: 10000
});

instance.interceptors.request.use(
  (config) => {
    let lang = Cookies.get("globalLang") ? JSON.parse(Cookies.get("globalLang")) : "zh-hans"
    let langMap = {
      'zh-hans': 'zh-CN',
      'zh-hant': 'zh-HK',
      'en': 'en-US',
    }
    config.headers.appVersion = "RESEARCH";
    config.headers['Accept-Language'] = langMap[lang];
    let token = sessionStorage.getItem('access_token') ? sessionStorage.getItem('access_token') : ''
    if(token){
      
      let expireTime = jwt_decode(token.split(' ')[1]).exp
      let now = new Date().getTime()
      now = now / 1000
      let isExpire = (expireTime - now) / 60 < 10
      if (jsBridge.isSupported('generateNewToken') && (expireTime === 0 || isExpire)) {
        jsBridge.run('generateNewToken', {
          callBack: ({accessToken})=>{
            if(accessToken){
              sessionStorage.setItem('access_token', `Bearer ${accessToken}`)
            }
            
          }
        });
      }
      if(config.url.indexOf('/open/operation/system/dict/keyText') < 0){
        config.headers['Authorization'] = token
      }
      
    }
    
    return config;
  }, (error) => {
    return Promise.reject(error);
  });

instance.interceptors.response.use(
  (response) => {
    let data = {};
    if (response.config && response.config.responseType === "blob") {
      data = response.data;
    } else if (response && response.data && response.data.code) {
      data = JSON.parse(JSON.stringify(response.data));
    } else {
      data = JSON.parse(JSON.stringify(response));
    }
    return data;
  }, (error) => {
    // return Promise.reject(error);
    let errorResponse = error.response && error.response.data ?  error.response.data : {}
    let status = error.response && error.response.status ? error.response.status : ''
    if(status === 401 && window && localStorage.getItem('source') !== 'app'){
      localStorage.setItem('access_token', '')
      return errorResponse
    }
    if(status === 401 && window && localStorage.getItem('source') === 'app'){
      showFailToast({ message: i18n.t('common.networkError') })
      errorResponse.msg = ''
      return errorResponse
    }
    if (error.message && error.message.includes('timeout')) {
      return showFailToast({ message: i18n.t('common.networkTimeout') })
    }
    if (errorResponse) {
        if (!status) {
          // showFailToast(errorResponse.msg)
        } else if (error.response) {
          // unShowToast 默认值为false 时展示Toast，否则展示 
          if (error.config && !error.config.unShowToast) {
            showFailToast({ message: errorResponse.data && errorResponse.data.msg || errorResponse.msg })  
          }
        } else {
          // unShowToast 默认值为false 时展示Toast，否则展示
          if (error.config && !error.config.unShowToast) { 
            showFailToast({ message: errorResponse.msg || i18n.t('common.networkError1')})
          }
        }
      } else {
        showFailToast({ message: i18n.t('common.networkError2') })
      }
    return errorResponse
  })

export default instance;
