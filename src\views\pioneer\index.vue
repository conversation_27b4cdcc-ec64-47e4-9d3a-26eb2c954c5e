<template>
  <div class="pioneer-wrap">
    <div class="content">
      <van-tabs v-model:active="tabActive"
                @change="handleTabsChange"
                :swipe-threshold="0"
      >
        <van-tab v-for="(item, index) in tabList"
                 :title="tabText(item)"
                 :key="index"
                 :title-class="item.cls"

        />
        <van-list v-model:loading="listLoading"
                  :finished="finished"
                  :finished-text="list.length ? $t('common.message.no-more') : ''"
                  :loading-text="$t('common.message.loading')"
                  :error-text="$t('common.message.loading-fail')"
                  @load="handleOnLoadData"
        >
          <van-pull-refresh v-model="refreshLoading" @refresh="handleOnRefresh">
            <template #pulling>
              <img
                :style="{height: '80%'}"
                :src="require('@/assets/gif/pullRefresh28.imageset/pullRefresh28.png')"
              />
            </template>
            <!-- 释放提示 -->
            <template #loosing>
              <img
                :style="{height: '80%'}"
                :src="require('@/assets/gif/pullRefresh28.imageset/pullRefresh28.png')"
              />
            </template>
            <template #loading>
              <loading></loading>
            </template>
            <Article v-for="(item, index) in list"
                     :key="index"
                     :item="item"
            />
          </van-pull-refresh>
        </van-list>
        <Nodata v-if="!list.length && finished" />
      </van-tabs>
    </div>
  </div>
</template>

<script>

import Article from "@/components/article";
import Nodata from "@/components/nodata";
import { showToast } from "vant";
import Mixins from "../../mixins";
import {
 PIONEERKEY
} from "../../constants/index";
import { getResearchList } from '@/api/index'
import { getSystemKeyText } from '@/api/common'
export default {
  components: { Article, Nodata},
  mixins: [ Mixins ],
  data() {
    return {
      list: [],
      tabList: [],
      listLoading: false,
      refreshLoading: false,
      finished: false,
      tabActive: 0,
      totalPages: null,
      currentPage: 0,
      currentSize: 20,
    }
  },
  methods: {
    async handleQueryGlobalConfig() {
      let params = {
          key: PIONEERKEY,
          tabId: this.$route.query.tabId || ''
        }
      let { data } = await getSystemKeyText(params)
      
      this.tabList = data;
      let strparams = {
        location: data[0].keyText,
        sort: "",
        page: 0,
        size: 20
      }
      
      let strRes = await getResearchList(strparams)
      let res = strRes.data ? strRes.data : {}
      this.listLoading = false;
      this.list = res.content;
      this.totalPages = res.totalPages;
    },
    handleResetParams() {
      this.totalPages = null;
      this.currentPage = 0;
      this.currentSize = 20;
      this.finished = true;
    },
    handleTabsChange() {
      this.handleResetParams();
      this.handleQueryCurrentList()
    },
    handleQueryCurrentList(isRefresh = false, isLoad = false, keyText = "") {
      
      if(!this.tabList.length) {
        this.handleQueryGlobalConfig();
      } else {
        const location = keyText ? keyText : this.tabList[this.tabActive].keyText;
        const params =  {
          location,
          sort: "",
          page: this.currentPage,
          size: this.currentSize
        }
        getResearchList(params).then(res => {
          this.listLoading = false;
          let data = res.data ? res.data : {}
          if(isRefresh) {
            showToast(this.$t("common.success.refresh"));
            this.refreshLoading = false;
          }

          if(isLoad) {
            data.content.forEach(d => this.list.push(d));
          } else {
            this.list = data.content;
          }
          this.totalPages = data.totalPages;
        })
      }
    },
    handleOnLoadData() {
      if((this.totalPages && this.currentPage >= this.totalPages)  || this.totalPages === 0) {
        this.finished = true;
        return;
      }
      
      this.handleQueryCurrentList(false, this.currentPage !== this.totalPages);
      this.currentPage++;
    },
    handleOnRefresh() {
      this.handleResetParams();
      this.handleQueryCurrentList(true);
    }
  }
};
</script>
<style lang="scss" scoped>
.pioneer-wrap {
  padding: 16px;
}
</style>