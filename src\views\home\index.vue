<template>
  <div class="home-wrap">
    <van-pull-refresh v-model="refreshLoading" @refresh="handleOnRefresh">
      <template #pulling>
        <img
          :style="{height: '80%'}"
          :src="require('@/assets/gif/pullRefresh28.imageset/pullRefresh28.png')"
        />
      </template>
      <!-- 释放提示 -->
      <template #loosing>
        <img
          :style="{height: '80%'}"
          :src="require('@/assets/gif/pullRefresh28.imageset/pullRefresh28.png')"
        />
      </template>
      <template #loading>
        <loading></loading>
      </template>
      <div class="banner">
        <swiper
          :spaceBetween="30"
          :centeredSlides="true"
          :autoplay="{
          delay: 3000,
          disableOnInteraction: false,
         }"
          :pagination="{
          clickable: true,
         }"
          :navigation="true"
          :modules="modules"
          class="mySwiper"
        >
          <swiper-slide v-for="(item, index) in dataSource.topBanner" :key="item.informationId">
            <div class="slide-item"
                 :key="index"
                 @click="handleJumpDetails(item)"
            >
              <img :src="item.coverUri" alt="banner">
              <div class="slide-title">
                {{ item.title }}
              </div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <div class="box-item ipo-read">
        <div class="head">
          <img src="../../assets/images/ipo.svg" alt="">
          <span class="text">{{ $t("home.ipo") }}</span>
        </div>
        <div class="cont">
          {{ dataSource.important ? dataSource.important.title : '-' }}
        </div>
      </div>
      <div class="box-item totay-stock">
        <div class="head">
          <img src="../../assets/images/<EMAIL>" alt="">
        </div>
        <div class="cont">
          <Article v-for="(item, index) in dataSource.todayStock"
                   :key="index"
                   :item="item"
          />
        </div>
      </div>
      <div class="relation">
        <div class="tab">
          <van-tabs v-model:active="tabActive"
                    @change="handleTabsChange"
                    :swipe-threshold="0"
          >
            <van-tab v-for="(item, index) in tabList"
                     :title="tabText(item)"
                     :key="index"
                     :title-class="item.cls"
            >
              <Article v-for="(item, index) in list"
                       :key="index"
                       :item="item"
              />
              <Nodata v-if="!list.length" />
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </van-pull-refresh>
  </div>
</template>

<script>
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Pagination, Autoplay, Navigation } from "swiper/modules";

import Mixins from "../../mixins";

import 'swiper/css';
import 'swiper/css/pagination';

import Article from "../../components/article";
import Nodata from "../../components/nodata";

import {
  PIONEERKEY,
} from "../../constants/index";
import { getSystemKeyText } from '@/api/common'
import { getResearchHome, getResearchList } from '@/api/index'

export default {
  components: {Swiper, SwiperSlide, Article, Nodata},
  mixins: [ Mixins ],
  data() {
    return {
      modules: [Autoplay, Pagination, Navigation],
      dataSource: {
        todayStock: [],
        topBanner: [],
        important: [],
      },
      tabActive: 0,
      totalPages: null,
      currentPage: 0,
      currentSize: 20,
      list: [],
      refreshLoading: false,
      tabList: []
    }
  },
  created() {
    this.handleQueryGlobalConfig();
  },
  mounted() {
    this.handleQueryHomeData();
  },
  methods: {
    handleJumpDetails(item) {
      this.$router.push(`details?informationId=${item.informationId}`)
    },
    handleQueryGlobalConfig() {
      const params = {
          key: PIONEERKEY,
          tabId: this.$route.query.tabId || ''
      }
      getSystemKeyText(params).then((data) => {
        this.tabList = data.data;
        this.handleQueryCurrentList();
      })
    },
    handleOnRefresh() {
      this.handleQueryHomeData();
      this.handleQueryCurrentList();
    },
    handleResetParams() {
      this.totalPages = null;
      this.currentPage = 0;
      this.currentSize = 20;
    },
    handleTabsChange() {
      this.handleResetParams();
      this.handleQueryCurrentList()
    },
    handleQueryHomeData() {
      getResearchHome().then((data) => {
        this.dataSource = data.data ? data.data :{};
      })
    },
    handleQueryCurrentList() {
      let location = this.tabList[this.tabActive].keyText;
      let params = {
          location,
          sort: "",
          page: this.currentPage,
          size: this.currentSize
        }

      getResearchList(params).then((res) => {
        if(res.code === 200 ){
          let data = res.data ? res.data : {}
          this.refreshLoading = false;
          this.list = data.content;
          this.totalPages = data.totalPages;
        }
        
      }).catch(err => console.log(err))
    },
  },
}
</script>
<style lang="scss" scoped>
.home-wrap {
  padding: 0 16px 16px 16px;
  .banner {
    margin-top: 16px;

    .swiper {
      width: 100%;
      height: 100%;

      .swiper-slide {
        height: 156px;
        text-align: center;
        font-size: 18px;
        display: flex;
        justify-content: center;
        align-items: center;

        .slide-item {
          width: 100%;
          height: 100%;
          position: relative;

          .slide-title {
            position: absolute;
            left: 16px;
            bottom: 24px;
            width: calc(100% - 16px);
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            font-weight: 600;
            font-size: 16px;
          }

          img {
            height: calc(100% - 16px);
            width: 100%;
            object-fit: cover;
          }
        }
      }

      .swiper-pagination {
        .swiper-pagination-bullet {
          width: 6px;
          height: 6px;
          background: var(--gray_03);
          border-radius: 3px;
          opacity: 1;
          &.swiper-pagination-bullet-active {
            width: 16px;
            background-color: var(--brand_01);
          }
        }
      }

      .swiper-pagination-fraction,
      .swiper-pagination-custom,
      .swiper-horizontal > .swiper-pagination-bullets,
      .swiper-pagination-bullets.swiper-pagination-horizontal {
        bottom: -4px;
      }
    }
  }

  .box-item {
    margin-top: 16px;
    background: var(--background);
    border-radius: 10px;
    &.ipo-read {
      padding: 16px;
      .head {
        display: flex;
        align-items: center;
        img {
          width: 24px;
          height: 24px;
        }
        .text {
          margin-left: 4px;
          font-size: 16px;
          color: var(--text_1st);
          font-weight: 600;
        }
      }
      .cont {
        margin-top: 8px;
        font-size: 14px;
        line-height: 22px;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    &.totay-stock {
      padding-bottom: 16px;
      .head {
        img {
          width: 100%;
        }
      }
      .cont {
        display: flex;
        flex-direction: column;
      }
    }
  }
  .relation {
    margin-top: 16px;
    .tab {}
    .van-tabs {
      .van-tabs__wrap {
        .van-tabs__nav {
          .van-tab {
            &.placeholder {
              display: none;
            }
          }
        }
      }
    }
  }
}

</style>
