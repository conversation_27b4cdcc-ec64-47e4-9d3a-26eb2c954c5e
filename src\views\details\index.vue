<template>
  <div class="details-wrap">
    <van-loading v-if="loading" type="spinner" color="#1989fa" />
    <div class="box" v-else>
      <div class="head">
        <div class="author">
          {{ `${infoData.author || '--'} | ${resetDate(infoData)}` }}
        </div>
      </div>
      <div class="title">{{ infoData.title || "" }}</div>
      <div class="content default-content-style" v-html="content" ></div>
      <div v-if='infoData.textPdf && isInEddidApp' class='check-more' @click='checkMore({pdfUrl: infoData.textPdf})'>
        <span class="pdf-name">{{ pdfName }}</span>
      </div>
      <div class="disclaimer-wrap">
        <div class="disclaimer">{{$t('common.disclaimer')}}</div>
        <div class="disclaimer-content">
          <div>{{$t('common.disclaimerContent')}}</div>
          <div class="translate-tip">
            * {{$t('common.translateTip')}}
          </div>
        </div>
        
      </div>
      
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import Mixins from "../../mixins";
import { getResearchDetails } from '@/api/index'
import { getCollectionInfo, addCollectionAPI, delCollectionAPI,  buryThePoint} from '@/api/common';
import collectSelected from '@/assets/images/collect_selected.png';
import collectDefault from '../../assets/images/collect_default.png';
import share from '@/assets/images/share.png';
import { getBas64 } from '@/utils/util';
export default {
  mixins: [ Mixins ],
  data() {
    return {
      infoData: {},
      content: "",
      loading: true,
      collectDefault,
      collectSelected,
      share,
      isActiveCollect: false,
      isLogin: false,
      isInEddidApp: false
    }
  },
  watch: {
    // isLogin: {
    //   immediate: true,
    //   handler(val){
    //     if(val){
    //       this.getToken();
    //     }
    //   }
    // }
  },
  computed: {
    ...mapState(['globalLang', 'globalTheme']),
    pdfName(){
      let item = this.infoData.textPdf
      return item.search('t=') ? item.split('t=')[item.split('t=').length - 1] : item.split('/')[item.split('/').length - 1] || this.$t('common.seeMore')
    }
  },
  created() {
    this.isInEddidApp = this.$jsBridge.isSupported('getAppInfo');
    if(this.$route.query.informationId) {
      this.handleQueryDetailsData(this.$route.query.informationId);
    }
    this.changeNavBar(this.isActiveCollect)
    if(sessionStorage.getItem('access_token')){
      this.isLogin = true
      this.getCollectionInfo()
    }
  },
  methods: {
    checkMore({ pdfUrl='' }){
      if(!pdfUrl) return;
      this.$jsBridge.run('openPdf', { pdfUrl: pdfUrl, pdfTitle: this.pdfName });
    },
    getToken(){
      this.$jsBridge.run('getAccessToken', {
        type: 'B',
        callback: (body) => {
          if(body.phone_token){
            this.phoneToken = body.phone_token;
            sessionStorage.setItem('access_token', `Bearer ${this.phoneToken}` )
            this.getCollectionInfo();
          }
        }
      });
    },
    login () {
      this.$jsBridge.run('startLogin', {
        type: '1A',
        callback: ({ login_state = false }) => {
          if(login_state) {
            this.isLogin = true;
            this.getToken()
          }
        }
      });
    },
    getCollectionInfo(){
      const params = {
        businessId: this.$route.query.informationId,
        collectionType: 'EDDID_REALTIME',
      }
      getCollectionInfo(params).then(res => {
        const { code, data, msg } = res;
        if (code === 200) {
          this.isActiveCollect = data.collected;
          this.changeNavBar(this.isActiveCollect)
        }
      })
    },
    handleParseFetchHtmlUrl(data) {
      if(this.globalLang === 'zh-hans') return data.newsTextUri.textUriCn;
      if(this.globalLang === 'zh-hant') return data.newsTextUri.textUriTc;
      if(this.globalLang === "en") return data.newsTextUri.textUriEn;

      return "";
    },
    handleQueryDetailsData(informationId) {
      const params = {
        informationId
      }
      getResearchDetails(params).then(res => {
        let data = res.data ? res.data : {}
        this.infoData = data;
        document.title = data.title || this.$t('router.detail')
        const url = this.handleParseFetchHtmlUrl(data);

        if(url) {
          fetch(url).then(result => result.text()).then(result => {
            this.content = result;
            
            this.loading = false;
          })
        }
        this.buryThePoint(informationId)
      })
    },
    buryThePoint(informationId){
      let params ={
        "author": this.infoData.author,
        "columns": this.infoData.menu,
        "newsId": informationId,
        "title": this.infoData.title
      }
      buryThePoint(params).then(res =>{

      })
    },
    changeNavBar(isActiveCollect=false) {
      this.$setNavBar.config('setCommonNavBar', {
        right: [
          // {
          //   showType: 'img',
          //   img: isActiveCollect ? this.collectSelected : this.collectDefault,
          //   callback: () => {
          //     this.collect()
          //   }
          // },
          {
            showType: 'img',
            img: this.share,
            callback: () => {
              this.onShared()
            }
          }
        ]
      })
    },
    onShared() {
      const { title, summary, coverUri } = this.infoData;
      const url = window.location.href // 过滤access_token、source、theme参数;
      this.$jsBridge.run('startShareUrlAndPicture', {
        type: 'B',
        title: title,
        content: summary,
        imageUrl: coverUri,
        url: `${url}&source=web&pageType=researDetail`, // sourc  e用于识别来源: web、app
        pageType: 'inforDetail',
        contentType: '研究院',
        callback: (body) => {
          // this.$sensor.common.contentShare({
          //   content_type: informationSource === 'EDDID_REALTIME' ? '艾德自研' : '第三方资讯', // '资讯详情页',
          //   content_title: realtimeTitle,
          //   activity_url: window.location.href,
          //   share_channel: body.channel
          // })
        }
      });
    },
    collect(el) {
      if(!this.isLogin){
        this.login();
        return;
      }
      console.log(this.infoData, 'this.infoData')
      const params = {
        businessId: this.$route.query.informationId,
        collectionType: 'EDDID_REALTIME',
      }
      switch (this.isActiveCollect) {
        case true:
          delCollectionAPI([params]).then((res) => {
            const { code, data } = res;
            if (code === 200) {
              // Toast({ content: this.$t('collectCancel') })
              this.isActiveCollect = false;
              this.changeNavBar(this.isActiveCollect)
              
            }
            // Toast({ content: data.msg })
          })
          break;
        default:
          addCollectionAPI(params).then((res) => {
            const { code, data } = res;
            if (code === 200) {
              // Toast({ content: this.$t('collectSuccess') })
              this.isActiveCollect = true;
              this.changeNavBar(this.isActiveCollect)
              
              // this.sensorHandle(el);
            }
          })
          break;
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.details-wrap {
  overflow-y: auto;
  overflow-x: hidden;
  height: 100vh;
  font-size: 16px;
  line-height: 28px;
  background: var(--background);
  padding: 16px;
  .scroll {
    height: 100%;
  }
  .van-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    height: 100%;
    min-height: 750px;
  }
  .box {
    .head {
      .author {
        color: var(--text_3rd);
        font-size: 13px;
      }
    }
    .title {
      color:var(--text_1st);
      font-weight: 600;
      font-size: 22px;
      margin: 12px 0;
    }
  }
}
.default-content-style {
  color: var(--text_1st);
  font-size: 16px;
  line-height: 28px;
}

.disclaimer{
  font-size: 16px;
  color:var(--text_1st);
  margin: 10px 0;
}
.disclaimer-content{
  background: var(--gray_05);
  border-radius: 5px;
  padding: 12px;
  color: var(--text_3rd);
  font-size: 12px;
  line-height: 1.8;
}
.translate-tip{
  color: var(--warning);
  margin-top: 5px;
}
.check-more {
    border-radius: 5px;
    font-size: 13px;
    line-height: 22px;
    padding: 10px;
    vertical-align: middle;
    color: var(--brand_01);
    background-color: var(--gray_05);
    margin-bottom: 16px;
    
    .pdf-name{
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      &::before {
        content: "";
        width: 18px;
        height: 18px;
        display: inline-block;
        background-repeat: no-repeat;
        background-size: contain;
        background-image: url(@/assets/images/pdf.png);
        background-position: center left;
        vertical-align: sub;
        margin-right: 5px;
      }
    }
    
  }
</style>
