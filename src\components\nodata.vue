<template>
  <div class="nodata-wrap">
    <div class="item">
      <img src="../assets/images/empty_massage.svg" alt="nodata">
      <p class="text">{{ text }}</p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    propsText: String,
    default: () => ''
  },
  data() {
    return {
      text: ""
    }
  },
  mounted() {
    this.text = this.propsText ? this.propsText : this.$t("common.message.nodata");
  }
}
</script>
<style lang="scss" scoped>
.nodata-wrap {
  padding: 32px 0;
  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    p {
      color: var(--text_3rd);
      font-size: 12px;
    }
  }
}

</style>
