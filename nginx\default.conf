server {
    listen       80;
    server_name  localhost;

    #charset koi8-r;
    access_log  /var/log/nginx/host.access.log  main;
    error_log  /var/log/nginx/error.log  error;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
        gzip on; #开启或关闭gzip on off
        gzip_static on;
        gzip_disable "msie6"; #不使用gzip IE6
        #gzip_min_length 100k; #gzip压缩最小文件大小，超出进行压缩（自行调节）
        gzip_buffers 4 16k; #buffer 不用修改
        gzip_comp_level 8; #压缩级别:1-10，数字越大压缩的越好，时间也越长
        gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png; #  压缩文件类型
        if ($request_filename ~ .*\.(htm|html)$)
        {
          add_header Cache-Control "no-cache, no-store";
        }
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
