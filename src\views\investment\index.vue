<template>
  <div class="investment-wrap">
    <div class="content">
      <van-tabs v-model:active="tabActive"
                @change="handleTabsChange"
                :swipe-threshold="0"
      >
        <van-tab v-for="(item) in tabList"
                 :title="tabText(item)"
                 :key="item.key"
                 :title-class="item.cls"
        />
        <van-list v-model:loading="listLoading"
                  :finished="finished"
                  :finished-text="list.length ? $t('common.message.no-more') : ''"
                  :loading-text="$t('common.message.loading')"
                  :error-text="$t('common.message.loading-fail')"
                  @load="handleOnLoadData"
        >
          <van-pull-refresh v-model="refreshLoading"
                            @refresh="handleOnRefresh"
          >
            <template #pulling>
              <img
                :style="{height: '80%'}"
                :src="require('@/assets/gif/pullRefresh28.imageset/pullRefresh28.png')"
              />
            </template>
            <!-- 释放提示 -->
            <template #loosing>
              <img
                :style="{height: '80%'}"
                :src="require('@/assets/gif/pullRefresh28.imageset/pullRefresh28.png')"
              />
            </template>
            <template #loading>
              <loading></loading>
            </template>
            <Article v-for="(item, index) in list"
                     :key="index"
                     :item="item"
            />
          </van-pull-refresh>
        </van-list>
        <Nodata v-if="!list.length && finished" />
      </van-tabs>
    </div>
  </div>
</template>

<script>
// import { ref } from 'vue';;
import Article from "../../components/article";
import Nodata from "../../components/nodata";
import { showToast } from "vant";
import { INVESTMENTKEY } from "../../constants/index";
import Mixins from "../../mixins";
import { getResearchList } from '@/api/index'
import { getSystemKeyText } from '@/api/common'
export default {
  components: { Article, Nodata },
  // setup() {
  //   const list = ref([]);
  //   const loading = ref(false);
  //   const finished = ref(false);
  //
  //   const onLoad = () => {
  //     // 异步更新数据
  //     // setTimeout 仅做示例，真实场景中一般为 ajax 请求
  //     setTimeout(() => {
  //       for (let i = 0; i < 10; i++) {
  //         list.value.push(list.value.length + 1);
  //       }
  //
  //       // 加载状态结束
  //       loading.value = false;
  //
  //       // 数据全部加载完成
  //       if (list.value.length >= 40) {
  //         finished.value = true;
  //       }
  //     }, 1000);
  //   };
  //
  //   return {
  //     list,
  //     onLoad,
  //     loading,
  //     finished,
  //   };
  // },
  data() {
    return {
      list: [],
      tabList: [],
      listLoading: false,
      refreshLoading: false,
      finished: false,
      tabActive: 0,
      totalPages: null,
      currentPage: 0,
      currentSize: 20,
    }
  },
  mixins: [ Mixins ],
  methods: {
    
    async handleQueryGlobalConfig() {
      let params = {
          key: INVESTMENTKEY,
          tabId: this.$route.query.tabId || ''
        }
      let { data } = await getSystemKeyText(params)
      
      this.tabList = data;
      let strparams = {
        location: data[0].keyText,
        sort: "",
        page: 0,
        size: 20
      }
      
      let strRes = await getResearchList(strparams)
      let res = strRes.data ? strRes.data : {}
      this.listLoading = false;
      this.list = res.content;
      this.totalPages = res.totalPages;
    },
    handleResetParams() {
      this.totalPages = null;
      this.currentPage = 0;
      this.currentSize = 20;
      this.finished = true;
    },
    handleTabsChange() {
      this.handleResetParams();
      this.handleQueryCurrentList();
    },
    handleQueryCurrentList(isRefresh = false, isLoad = false) {
      if(!this.tabList.length) {
        this.handleQueryGlobalConfig();
      } else {
        let location = this.tabList[this.tabActive].keyText;
        const params =  {
          location,
          sort: "",
          page: this.currentPage,
          size: this.currentSize
        }
        getResearchList(params).then(res => {
          this.listLoading = false;
          let data = res.data ? res.data : {}
          if(isRefresh) {
            showToast(this.$t("common.success.refresh"));
            this.refreshLoading = false;
          }

          if(isLoad) {
            data.content.forEach(d => this.list.push(d));
          } else {
            this.list = data.content;
          }
          this.totalPages = data.totalPages;
        })
      }
    },
    handleOnLoadData() {
      // if(this.currentPage === this.totalPages) {
      //   this.finished = true;
      //   return;
      // }
      if((this.totalPages && this.currentPage >= this.totalPages)  || this.totalPages === 0) {
        this.finished = true;
        return;
      }
      this.handleQueryCurrentList(false, this.currentPage !== this.totalPages);
      this.currentPage++;
    },
    handleOnRefresh() {
      this.handleResetParams();
      this.handleQueryCurrentList(true);
    }
  },
};
</script>
<style lang="scss" scoped>
.investment-wrap {
  padding: 16px;
}
</style>