{"name": "app-information", "version": "0.1.0", "private": true, "scripts": {"start": "npm run local", "local": "vue-cli-service serve --mode qa", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "build:dev": "vue-cli-service build --mode develop", "build:uat": "vue-cli-service build --mode uat", "build:qa": "vue-cli-service build --mode qa", "build:sit": "vue-cli-service build --mode sit", "build:prd": "vue-cli-service build"}, "dependencies": {"axios": "^1.4.0", "babel-eslint": "^10.1.0", "callapp-lib": "^3.5.3", "core-js": "^3.8.3", "dateformat": "^5.0.3", "js-cookie": "^3.0.5", "jwt-decode": "^3.1.2", "moment": "^2.29.4", "postcss-pxtorem": "^6.0.0", "sass": "^1.64.2", "sass-loader": "^13.3.2", "swiper": "^10.1.0", "vant": "^4.6.3", "vconsole": "^3.15.1", "vue": "^3.2.13", "vue-i18n": "^9.2.2", "vue-router": "^4.2.4", "vuex": "^4.1.0", "xgplayer": "^3.0.21", "xgplayer-hls": "^3.0.22"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-loader": "^4.0.2", "eslint-plugin-vue": "^8.0.3"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}