#!/bin/sh

markdown()
{
TITLE="# 项目：[${CI_PROJECT_NAME}](${CI_PROJECT_URL})"

if [ "$1" = "start" ]; then
OPERATION="## ☕ 开始 - [查看详情](${CI_PIPELINE_URL})"
elif [ "$1" = "build-failure" ]; then
OPERATION="## 😭 编译失败！ - [查看详情](${CI_JOB_URL})"
elif [ "$1" = "build-image-failure" ]; then
OPERATION="## 😭 镜像失败！ - [查看详情](${CI_JOB_URL})"
elif [ "$1" = "image-done" ]; then
OPERATION="## 🐳 镜像完成！ - [查看详情](${CI_PIPELINE_URL})"
elif [ "$1" = "image-done-with-cache" ]; then
OPERATION="## 🐳 镜像完成(使用缓存加速)！ - [查看详情](${CI_PIPELINE_URL})"
elif [ "$1" = "image-done-with-tag-exists" ]; then
OPERATION="## 🐳 镜像完成(Tag已经存在)！ - [查看详情](${CI_PIPELINE_URL})"
elif [ "$1" = "deploy-failure" ]; then
OPERATION="## ⛔ 部署失败！ - [查看详情](${CI_JOB_URL})"
elif [ "$1" = "deploy-done" ]; then
OPERATION="## ⚓ 部署完成！ - [查看详情](${CI_PIPELINE_URL})"
else
cat <<EOF
${TITLE}
\n\n## ❓ 操作未定义！
EOF
exit 0
fi

if [ -z "$CI_COMMIT_TAG" ]; then
RELEASE_INFO="> 分支：${CI_COMMIT_REF_NAME} sha:[${CI_COMMIT_SHA}](${CI_PROJECT_URL}/commit/${CI_COMMIT_SHA})"
else
RELEASE_INFO="> 版本号(tag)：[${CI_COMMIT_TAG}](${CI_PROJECT_URL}/tags/${CI_COMMIT_TAG})"
fi

RELEASE_COMMENT="> 备注： $(echo "$CI_COMMIT_MESSAGE" | sed -e ':a;N;$!ba;s/\n/\n> /g' | sed -e 's/\\n/\n> /g')"
OPERATOR="> 操作人：${GITLAB_USER_NAME}"

cat <<EOF
${TITLE}
\n\n${OPERATION}
\n\n${RELEASE_INFO}
\n\n${RELEASE_COMMENT}
\n\n${OPERATOR}
\n
EOF
}

MARKDOWN=$(markdown "$1" | sed -e 's/"/\\"/g')
HTTP_DATA="{\"msgtype\":\"markdown\",\"markdown\":{\"title\":\"${CI_PROJECT_NAME} $1\",\"text\":\"${MARKDOWN}\"}}"

wget -qO- --post-data="${HTTP_DATA}" \
  --header='Content-Type: application/json' \
  "https://oapi.dingtalk.com/robot/send?access_token=${DINGDING_TOKEN}" || true