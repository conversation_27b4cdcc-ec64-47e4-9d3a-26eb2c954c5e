<template>
    <img
      class="doge"
      :src="require(`@/assets/gif/pullRefresh${imageIndex}.imageset/pullRefresh${imageIndex}.png`)"
    />
</template>
<script>
export default {
  data() {
    return {
     
      imageIndex: 1,
      timer: ''
    }
  },
  mounted(){
    this.play()
  },
  methods: {
    play(){
      this.timer = setInterval(()=>{
          if(this.imageIndex < 65){
            this.imageIndex++ 
          }else{
            this.imageIndex = 1
          }
      },64)
    }
  },
  beforeDestroy(){
    clearInterval(this.timer)
  }
};
</script>
<style lang="scss" scoped>
.doge{
  height: 80%;
}
</style>