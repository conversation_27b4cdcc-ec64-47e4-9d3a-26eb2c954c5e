// 正文默认样式
.default-content-style {
  p,
  div,
  h1,
  h2,
  h3,
  h4,
  h5,
  span,
  section,
  strong,
  a {
    background-color: transparent !important;
    word-wrap: break-word;
  }

  a {
    text-decoration: none;
  }

  img {
    width: 100% !important;
    height: auto !important;
  }

  strong {
    font-weight: 600 !important;
  }

  table {
    width: auto !important;
    table-layout: fixed !important;
    word-break: break-all !important;

    thead,
    tbody {
      tr,
      td {
        width: auto !important;
        white-space: break-spaces !important;
      }
    }
  }
}
.default-font-style {
  font-weight: 400;
  color: #666;

  p,
  div,
  h1,
  h2,
  h3,
  h4,
  h5,
  span,
  section,
  a {
    color: #666 !important;
    font-size: 30px !important;
    line-height: 56px !important;
  }
}
// 正文默认样式
.dark-default-content-style {
  color: #8a8a8a !important;
  background-color: #171717 !important;

  p,
  div,
  h1,
  h2,
  h3,
  h4,
  h5,
  span,
  section,
  a,
  strong {
    color: #8a8a8a !important;
    background-color: #171717 !important;
  }

  table {
    thead {
      background-color: #1a1c22 !important;

      tr,
      td {
        color: #92929e !important;
        background-color: #1a1c22 !important;
      }
    }

    tbody {
      background-color: #171717 !important;

      tr,
      td {
        color: #6d748f !important;
        background-color: #171717 !important;
      }
    }

    thead,
    tbody {
      tr,
      td {
        border: 1px solid #1d1d1d !important;
        text-align: center !important;
      }
    }

    td > *,
    td > * > * {
      background-color: transparent !important;
    }
  }
}

body {
  background: var(--gray_05);
}
.van-toast{
  width: fit-content;
  min-width: 80px;
  max-width: 80%;
  min-height: auto;
  text-align: left;
}
