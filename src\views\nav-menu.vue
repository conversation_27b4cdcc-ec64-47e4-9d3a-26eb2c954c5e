<template>
    <ul class="nav">
      <li v-for="item in navList" :key="item.path" @click="$router.push(item.path)">{{item.name}}</li>
    </ul>
</template>
<script>
export default {
  data() {
    return {
      navList: [
        {name: this.$t('router.investment'), path: '/investment'},
        {name: this.$t('router.pioneer'), path: '/pioneer'},
        {name: this.$t('router.company'), path: '/company'},
        {name: this.$t('router.strategy'), path: '/strategy'},
        {name: this.$t('router.home'), path: '/home'},
        {name: this.$t('router.videoDetail'), path: '/video/detail'},
      ]
    }
  },
  methods: {
    
  }
};
</script>
<style lang="scss" scoped>
.nav{
  margin: 20px;
  font-size: 15px;
}
.nav li{
  float: left;
  width: 50%;
  margin-bottom: 20px;
}
</style>