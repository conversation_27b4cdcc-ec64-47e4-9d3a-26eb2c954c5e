import runner from "./shared/runner";
import { commitShareInfo } from "@/api/common"
export default {
  isSupported: runner.isSupported,

  run(name, options = {}) {
    if (this.isSupported(name, options)) {
      runner.run(name, options);
    }
  },
};
const jsBridge = {
  isSupported: runner.isSupported,
  run(name, options = {}) {
    if (this.isSupported(name, options)) {
      // runner.run(name, options);
      if (localStorage.getItem('isInternational') === 'true' && (options.url && options.url.indexOf('inter=') < 0)) {
        options.url = `${options.url}${options.url.includes('?') ? '&' : '?'}inter=inter`
      }
      let param = {
        "content": options.content,
        "imageUrl": options.imageUrl,
        "title": options.title,
        "url": options.url
      }

      if(name === 'startShareUrl'){
        commitShareInfo(param).then(res => {
          options.url = res.data
          runner.run(name, options);
        })
      }else{
        runner.run(name, options);
      }
    }
  },
};
/**
 * @example
 *   // eg1:
 *   // this.$setNavBar.config("setCommonNavBar", {
 *   // eg2:
 *   this.$setNavBar.config({
 *     right: [
 *      {
 *        showType: "img", // text img nativeIcon
 *        text: "分享",
 *        img: Base64, // Base64 格式
 *        nativeIcon: "", // eg: BACK/CLOSE/SHARE
 *        callback: () => {
 *          // todo something
 *        },
 *      },
 *      {
 *        showType: "text", // text img nativeIcon
 *        text: "分享",
 *        img: Base64, // Base64 格式
 *        nativeIcon: "", // eg: BACK/CLOSE/SHARE
 *        callback: () => {
 *          // todo something
 *        },
 *      },
 *    ],
 *    backCallback: () => {
 *      // todo something
 *    },
 *    closeCallback: () => {
 *      // todo something
 *    },
 *  });
 *
 */
const setNavBar = {
  isSupported: runner.isSupported,
  /**
   * @param  {...any} arg 支持多个参数
   */
  config(...arg) {
    const name = arg.length > 1 ? arg[0] : "setCommonNavBar";
    const options = arg.length > 1 ? arg[1] : arg[0];
    console.log(name, options, '>>>>')
    if (this.isSupported(name, options)) {
      
      Object.assign(options, { setNavBar: true });
      runner.run(name, options);
    }
  },
};

export { jsBridge, setNavBar };
