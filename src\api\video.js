import axios from "@/utils/request";
const baseUrl = '/open/operation'
export function getVideoDetail(params) { 
  return axios.get(`${baseUrl}/research/custom/video/detail`, { params })
}
export function getRecVideoList(params) { 
  return axios.get(`${baseUrl}/research/custom/video/list`, { params })
}
export function buryVideoThePoint(params) {
  return axios.post(`${baseUrl}/news/analysis/report/subdirectory/video`, params)
}