:root{
  // 中性色
  --gray_01: #4B596B;
  --gray_02: #C8CDD5;
  --gray_03: #E5E6EC;
  --gray_04: #F2F3F5;
  --gray_05: #F7F8FA;
  --gray_05_opacity: rgba(247, 248, 250, 0.8);

  // 分割线
  --line_01:#E5E6EB;
  --line_02:#C8CDD5;

  // 品牌色
  --brand_01:#012169;
  --brand_02:#3D63A5;
  --brand_03:#6A8EC3;
  --brand_04:#A4BEE1;
  --brand_05:#E8F3FF;
  --brand_06:#F9FBFF;
  --brand_07:#F8D000;
  --brand_opacity_01: #012169;
  --brand_opacity_02: #F8D000;

  // 数据可视化
  --data_01:#5B8FF9;
  --data_02:#61DDAA;
  --data_03:#65789B;
  --data_04:#F6BD16;
  --data_05:#7262FD;
  --data_06:#78D3F8;
  --data_07:#9661BC;
  --data_08:#F6903D;
  --data_09:#008685;
  --data_10:#F08BB4;

  // 文字色
  --text_1st:#1C212A;
  --text_2nd:#4B596B;
  --text_3rd:#83909D;
  --text_4rd:#B6C2D0;
  --text_5th:#F1F3F7;
  --text_disabled:#C8CDD5;
  --text_opacity: rgba(241, 243, 247, 0.6);

  // 涨跌色
  --green:#0ACCA0;
  --red:#FF0B34;

  // 背景色
  --background:#FFFFFF;
  --background_01:#FFFFFF;  // !!!防止和全局透明背景色冲突

  // 功能色
  --error: #FF4D4F;
  --link: #178FFF;
  --success: #52C41A;
  --warning: #FAAD13;

  // 遮罩
  --mask: rgba(0,0,0,0.25);
}
