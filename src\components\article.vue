<template>
  <div class="article-wrap">
    <div class="item" @click="handleJumpTemplate">
      <div class="title" :class="{ active: item.coverUri !== '' }">
        <div>
          <span class="text">{{ item.title }}</span>
          <div class="meta">{{ `${item.author} · ${resetDate(item)}` }}</div>
        </div>
        
        <img :src="item.coverUri" alt="" v-if="item.coverUri !== ''">
      </div>
      
    </div>
  </div>
</template>

<script>
import dataformat from "dateformat";
import Mixins from "../mixins";
export default {
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  mixins: [ Mixins ],
  methods: {
    handleJumpTemplate() {
      this.$startEdWeb({url: `/details?informationId=${this.item.informationId}`})
    }
  }
}
</script>
<style lang="scss" scoped>
.article-wrap {
  padding: 16px;
  background: var(--background);
  .item {
    display: flex;
    flex-direction: column;
    .title {
      color: var(--text_1st);
      font-weight: 500;
      font-size: 16px;
      display: flex;
      align-items: center;
      &.active {
        .text {
          margin-right: 16px;
          -webkit-line-clamp: 2;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 12px;
        }
      }
      img {
        margin-left: auto;
        width: 88px;
        border-radius: 5px;
      }
    }
    
  }
  .meta {
    font-weight: 400;
    margin-top: 8px;
    color: var(--text_3rd);
    font-size: 12px;
  }
}

</style>
