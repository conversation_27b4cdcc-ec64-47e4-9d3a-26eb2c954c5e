<template>
  <router-view></router-view>
</template>

<script>
import { themeDATA } from '@/utils/theme';
import Cookies from '@/utils/cookies';
import { mapState } from 'vuex';
export default {
  computed:{
    ...mapState(['globalTheme']),
  },
  watch: {
    $route:{
      handler(to, form){
        document.title = to.meta.title ? to.meta.title : '';
        themeDATA[this.globalTheme] && Object.keys(themeDATA[this.globalTheme]).forEach((item) => {
          document.documentElement.style.setProperty(item, themeDATA[this.globalTheme][item]);
        });
      }
    }
  },
  created() {
    window.document.documentElement.setAttribute("data-theme", this.globalTheme);
    const hrefAry = window.location.href.split("?");

    if(hrefAry.length > 1) {
      let query = hrefAry[1];
      const urlSearch = new URLSearchParams(query);
      const langQuery = urlSearch.get("lang");
      if(langQuery) {
          this.$i18n.locale = langQuery;
          this.$store.commit("commit_app_globalLang", langQuery);
      }
    }
  }
}
</script>
