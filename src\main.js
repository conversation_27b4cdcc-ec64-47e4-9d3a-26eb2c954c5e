import { createApp } from 'vue';
import Vant from "vant";
import router from "./router";
import store from "./store";
import i18n from "./i18n";
import App from './App.vue';
import { jsBridge, setNavBar } from '@/utils/jsBridge';
import { startEdWeb } from '@/utils/util';
import "vant/lib/index.css";
import "@/css/index.scss";
import '@/utils/responsive';
import Loading from "@/components/loading";
if(process.env.NODE_ENV && process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'uat'){
  const Vconsole = require('vconsole')
  // eslint-disable-next-line no-unused-vars
  new Vconsole()
}



jsBridge.run('getAccessToken', {
  type: 'C',
  callback: ({ quotation_token, quotation_ip, rights, market_type,  phone_token }) => {
    if(phone_token){
      sessionStorage.setItem('access_token', `Bearer ${phone_token}` )
    }else{
      sessionStorage.setItem('access_token', `` )
    }
    
  }
});
const app = createApp(App).use(Vant).use(router).use(store).use(i18n);
app.config.globalProperties.$jsBridge = jsBridge
app.config.globalProperties.$setNavBar = setNavBar
app.config.globalProperties.$startEdWeb = startEdWeb
app.component('Loading', Loading)
app.mount('#app')
