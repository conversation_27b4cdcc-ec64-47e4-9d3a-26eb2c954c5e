<template>
  <div class="details-wrap">
    <van-loading v-if="loading" type="spinner" color="#1989fa" />
    <div class="box" v-else ref="inPlayer">
      <in-video :cover="infoData.coverUri" :url="infoData.videoUri" :isLive="isLive"></in-video>
      <van-button @click="showPopUpFn">右侧弹出</van-button>
      <moveLive v-model="rightVisible" />

      <div class="content">
        <div class="title">{{ infoData.videoTitle || "" }}</div>
        <div class="head">
          <div class="author">
            {{ `${infoData.videoAuthor || '--'} | ${resetDate(infoData)}` }}
          </div>
        </div>
        <div class="comment default-content-style" v-html="infoData.videoIntroduction" ></div>
        <div class="disclaimer">
          <div>*{{ $t('video.investmentRisks')}}*</div>
          <div>{{$t('video.disclaimer')}}</div>
        </div>
        <div class="bottom">
          <div class="rela-title" v-if="recommendList.length">{{$t('video.relativeRec')}}</div>
          <div class="recommend-list">
            <div class="recommend-item" v-for="(item, index) in recommendList" :key="index" @click="toDetail(item)">
              <div class="cover-content">
                <img class="rec-cover" :src="item.coverUri" alt="">
                <div class="duration">
                  <img class="play-icon" :src="require('@/assets/images/play_mini.png')" alt="">
                  <span>{{item.duration}}</span>
                </div>
              </div>
              <div class="recommend-content">
                <div class="recommend-title">{{ item.videoTitle || "" }}</div>
                <div class="recommend-time">{{  resetDate(item) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import Mixins from "../../mixins";
import InVideo from "@/components/in-video.vue";
import moveLive from "./components/move-live.vue";
import { getVideoDetail, getRecVideoList, buryVideoThePoint } from '@/api/video'
import { debounce } from '@/utils/util'
export default {
  mixins: [ Mixins ],
  data() {
    return {
      loading: false,
      videoId: '',
      infoData: {
        // title: '投资笔记特别场 - 解构加密货币大升之谜 |主持：SaSa | 嘉宾：萧佩铃 三星ETF 投资策略师 | 陈政深 艾德金融董事',
        // author: 'Eddid Channel',
        // releaseTime: '2024-03-28',
        // coverUrl: 'https://middleware-information-qa-public.oss-cn-shenzhen.aliyuncs.com/9277e7e3-cfe5-43bb-a755-d3ddbee4d7aa.jpg?t=001.jpeg',
        // content: '*投资有风险, 交易需谨慎   #比特币 #国泰 #美股 #基金 #ETF #强积金 #旅游股 #中国经济 ',
        // url: 'https://lf3-static.bytednsdoc.com/obj/eden-cn/nupenuvpxnuvo/xgplayer_doc/xgplayer-demo.mp4'
      },
      isLive: false,
      recommendList: [],
      rightVisible: false,
      debounceShowPopUp: null

    }
  },
  components:{
    InVideo,
    moveLive
  },
  watch: {
    '$route'(to, from) {
      // 仅当 id 变化时触发
      if (to.query.videoId !== from.query.videoId) {
        this.$refs.inPlayer && this.$refs.inPlayer.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
        this.initData()
      }
    }
    // isLogin: {
    //   immediate: true,
    //   handler(val){
    //     if(val){
    //       this.getToken();
    //     }
    //   }
    // }
  },
  computed: {
    ...mapState(['globalLang', 'globalTheme']),
  },
  created() {

  },
  mounted(){
    this.debounceShowPopUp = debounce(this.showPopUp, 20, true, true)
    this.initData()
  },
  methods: {
    showPopUpFn(){
      this.debounceShowPopUp()
    },
    async initData(){
      this.videoId = this.$route.query.videoId || 'abe36344-08ce-476c-9374-d76a955ae001'
      await this.getVideoDetail()
      await this.getRecVideoList()
      await this.buryVideoThePoint()
    },
    async getVideoDetail(){
      
      await getVideoDetail({videoId: this.videoId}).then((res) => {
        let data = res.data ? res.data : {}
        console.log(data, 'data>>>>????')
        // this.isLive = true
        // data.videoUri = 'https://ems-pull-qa.eddidyzt.com/ems/d02de31545aa472c9d16df68bcaccf71.m3u8?auth_key=1750245887-54ecce9e8db840d69970501578f3634f-0-044a2d18f791ae978104959ead3a2ba8'
        
        this.infoData = data
      })
    },
    async buryVideoThePoint(){
      let params ={
        "author": this.infoData.videoAuthor,
        "columnId": this.infoData.relatedColumn,
        "videoId": this.infoData.videoId,
        "title": this.infoData.videoTitle
      }
      await buryVideoThePoint(params).then(res =>{

      })
    },
    async getRecVideoList(){
      let params = {
        page: 0,
        size: 10,
        relatedColumn: this.infoData.relatedColumn,
        videoId: this.infoData.videoId,
      }
      await getRecVideoList(params).then(res => {
        this.recommendList = res.data ? res.data.content : []
      })
    },
    toDetail(item){
      this.$router.push({
        path: '/video/detail',
        query: {
          videoId: item.videoId
        }
      })
    },
    showPopUp(){
      this.rightVisible = !this.rightVisible
    }
  },
  beforeUnmount(){
    this.debounceShowPopUp.cancel()
  }
  
}
</script>
<style lang="scss" scoped>
.details-wrap{
  overflow-y: auto;
  overflow-x: hidden;
  height: 100vh;
  font-size: 12px;
  line-height: 18px;
  background: var(--background);
  .content{
    padding: 12px 16px 10px;
  }
  .title {
    color:var(--text_1st);
    font-weight: 600;
    font-size: 16px;
    margin: 4px 0;
    line-height: 26px;
  }
  .author, .comment {
    color: var(--text_2nd);
    font-size: 12px;
  }
  .comment{
    margin-top: 12px;
  }
  .disclaimer{
    background: var(--gray_05);
    padding: 12px;
    color: var(--text_3rd);
    margin-top: 12px;
  }
  .bottom{
    margin-top: 12px;
  }
  .recommend-list{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  .rela-title{
    font-size: 18px;
    font-weight: 600;
    color: var(--text_1st);
  }
  .recommend-item{
    width: 164px;
    height: 181px;
    background: var(--gray_05);
    margin-top: 16px;
    border-radius: 10px;
    
    .rec-cover{
      width: 100%;
      height: 100%;
      border-radius: 10px 10px 0px 0px;
      position: relative;
    }
    .recommend-content{
      padding: 8px;
    }
    .recommend-title{
      font-size: 14px;
      line-height: 22px;
      height: 44px;
      margin-bottom:  8px;
      color: var(--text_1st);
      font-weight: 600;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .duration{
      position: absolute;
      bottom: 8px;
      left: 8px;
      width: 60px;
      height: 20px;
      line-height: 20px;
      font-size: 11px;
      background: rgba(0,0,0,0.4);
      border-radius: 3px;
    }
    .cover-content{
      width: 100%;
      height: 91px;
      border-radius: 10px 10px 0px 0px;
      position: relative;
      color: var(--background);
    }
    .play-icon{
      vertical-align: bottom;
      width: 12px;
      padding: 4px 6px;
    }
  }
  .recommend-time{
    color: var(--text_3rd);
  }
  
}
</style>
