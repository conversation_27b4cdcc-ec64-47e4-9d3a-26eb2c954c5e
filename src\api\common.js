import axios from "@/utils/request";
export function getSystemKeyText(params) { // 获取字典
  return axios.get(`/open/operation/tab/keyText`, { params })
}

// 协议条款内容
export function commitShareInfo(params) {
  return axios.post(`${window.serverUrl || process.env.VUE_APP_OPERATION_API}/open/info/content/share`, params)
}
// 研究院-我的收藏-添加
export function addCollectionAPI(params) {
  return axios.post(`/open/account/rsCollection/add`, params)
}
// 研究院-我的收藏-取消收藏-h5详情页
export function delCollectionAPI(params) {
  return axios.delete(`/open/account/rsCollection/del`, {data: params })
}
// 研究院-我的收藏-h5查询是否已收藏
export function getCollectionInfo(params) { 
  return axios.get(`/open/account/rsCollection/collected`, { params })
}

// 资讯栏目详情埋点
export function buryThePoint(params) {
  return axios.post(`/open/operation/news/analysis/report/subdirectory`, params)
}