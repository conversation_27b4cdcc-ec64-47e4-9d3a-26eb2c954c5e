import {mapState} from "vuex";
import dataformat from "dateformat";
import moment from "moment";
export default {
  data() {
    return {
    }
  },
  computed: {
    ...mapState({
      globalLang: "globalLang"
    }),
    tabText() {
      return (item) => {
        if(this.globalLang === 'zh-hans') return item.noteText;
        if(this.globalLang === 'zh-hant') return item.noteTextTc;
        if(this.globalLang === "en") return item.noteTextEn;

        return item.noteText;
      }
    },
    resetDate() {
      return (item) => {
        if(moment().diff(moment(item.releaseTime), 's') < 60){
          return this.$t("common.time.now");
        }
        let gap = moment().diff(moment(item.releaseTime), 'm')
        if(gap < 60){
          return `${gap} ${this.$t("common.time.m-ago")}`;
        }
        if((moment().diff(moment(item.releaseTime), 'h')) >= 1 && (moment(moment().format('YYYY-MM-DD 00:00')).diff(moment(item.releaseTime), 'h') <= 0) && (moment(moment().format('YYYY-01-01 00:00')).diff(moment(item.releaseTime), 's') < 0)){
          return this.$t('common.time.today')+ ' ' +moment(item.releaseTime).format('HH:mm')
        }
        if((moment().diff(moment(item.releaseTime), 'h')) >= 1 && (moment(moment().format('YYYY-MM-DD 00:00')).diff(moment(item.releaseTime), 'h') > 0) && (moment(moment().format('YYYY-01-01 00:00')).diff(moment(item.releaseTime), 's') < 0)){
          return moment(item.releaseTime).format('MM-DD HH:mm')
        }
        if((moment(moment().format('YYYY-01-01 00:00')).diff(moment(item.releaseTime), 's') > 0)){
            return moment(item.releaseTime).format('YYYY-MM-DD HH:mm')
        }

        return item.releaseTime;
      }
    }
  },
}
