import { createI18n } from "vue-i18n";
import zh from "./langs/zh-hans/index";
import en from "./langs/en/index";
import hk from "./langs/zh-hant/index";
import Cookies from "@/utils/cookies";
import { urlParse } from '@/utils/util';
import store from '@/store';
// let lang = Cookies.get("globalLang") ? JSON.parse(Cookies.get("globalLang")) : "zh-hans";
const lang = urlParse(window.location.search).lang || store.state.globalLang;
const i18n = createI18n({
    locale: lang,
    fallbackLocale: 'zh-hans',
    silentFallbackWarn: true,
    messages: {
        "zh-hans": zh,
        "zh-hant": hk,
        en
    }
})

export default i18n;
