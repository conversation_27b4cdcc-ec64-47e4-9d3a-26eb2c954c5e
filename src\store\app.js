import Cookies from '@/utils/cookies';

const state = {
  globalTheme: Cookies.get("theme") ? JSON.parse(Cookies.get("theme")) : "light",                           // 主题
  globalLang: Cookies.get("globalLang") ? JSON.parse(Cookies.get("globalLang")) : "zh-hans",                     // 语言
}

const mutations = {
  commit_app_globalTheme: (state, globalTheme) => {
    state.globalTheme = globalTheme;
    Cookies.set("theme", JSON.stringify(globalTheme));
    window.document.documentElement.setAttribute("data-theme", globalTheme);
  },
  commit_app_globalLang: (state, globalLang) => {
    state.globalLang = globalLang;
    console.log(globalLang, 'globalLang')
    Cookies.set("globalLang", JSON.stringify(globalLang));
  }
};

const actions = {
  
};

export default {
  state,
  mutations,
  actions
}
