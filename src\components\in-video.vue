<template>
  <div class="player-wrap" v-if="this.url">
    <div :id='config.id' ref="player">
    </div>
    <div v-if="playStatus && aliveTipEnum[playStatus]" class="player-mask">
      <div class="player-error-text">{{ aliveTipEnum[playStatus].label}}</div>
    </div>
  </div>
</template>

<script>
import Player, { Events } from 'xgplayer';
import HlsPlugin from 'xgplayer-hls'
import 'xgplayer/dist/index.min.css';
import PlaySvg from '@/assets/images/play.svg'
export default {
  components:{

  },
  props: {
    cover: {
      type: String,
      default: ''
    },
    isLive: {
      type: Boolean,
      default: false
    },
    url: {
      type: String,
      default: ''
    }
  },
  watch:{
    url: {
      immediate: true,
      handler(val){
        if(val){
          this.config.url = val
          this.config.poster = this.cover
          this.$nextTick(()=>{
            this.init()
          })
        }
      }
    }
  },
  data() {
    const aliveTipEnum = {
      end: {
        label: this.$t('video.liveEnded'),
        // src: require('./images/status/end.png')
      },
      // leave: {
      //   label: this.$t('主播离开一会儿，请稍后…'),
      //   src: require('./images/status/leave.png')
      // },
      network: {
        label: this.$t('video.liveDisconnected'),
        // src: require('./images/status/network.png')
      },
      // wait: {
      //   label: this.$t('主播离开一会儿，请稍后…'),
      //   src: require('./images/status/leave.png')
      // },
      down: {
        label: this.$t('video.liveRemoved'),
        // src: require('./images/status/down.png')
      }
    }
    return {
      // source: "https://lf3-static.bytednsdoc.com/obj/eden-cn/nupenuvpxnuvo/xgplayer_doc/xgplayer-demo.mp4",
      config: {
        id: 'vs',
        width: '100%',
        height: '4.2rem',
        // icons:{
        //   MyPlayIcon: PlaySvg
        // },
        // autoplayMuted: true, // 静音播放
        autoplay: true,
        url: this.url || '',
        poster: this.cover || '',
        // poster: "https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/byted-player-videos/1.0.0/poster.jpg",
        fitVideoSize: 'fixWidth',
        // miniprogress: true,
        Player: null,
        controls: {
          initShow: true,
          // flex: true
        },
        commonStyle:{
          playedColor: 'linear-gradient(270deg, #FDEC53 0%, #27FFEE 100%)',
          sliderBtnStyle: {
            // background: 'green'
          }
        },
        // plugins: [HlsPlugin],
        // marginControls: true,
        playbackRate: [0.5, 1, 1.25, 1.5, 2]
      },
      aliveTipEnum,
      playStatus: '',
    }
  },
  beforeCreate(){
    window.ResizeObserver = class ResizeObserver extends window.ResizeObserver {
      constructor(callback) {
        let timer = null;
        const debouncedCallback = function () {
          let context = this;
          let args = arguments;
          clearTimeout(timer);
          timer = setTimeout(function () {
            callback.apply(context, args);
          }, 16);
        };
        super(debouncedCallback);
      }
    };
    // 添加iOS音频解锁
  const unlockAudio = () => {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const unlock = () => {
      const buffer = audioContext.createBuffer(1, 1, 22050);
      const source = audioContext.createBufferSource();
      source.buffer = buffer;
      source.connect(audioContext.destination);
      source.start(0);
      document.removeEventListener('touchstart', unlock);
      document.removeEventListener('touchend', unlock);
    };
    document.addEventListener('touchstart', unlock, false);
    document.addEventListener('touchend', unlock, false);
  };
  
  // 检测iOS设备并应用解锁
  if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
    unlockAudio();
  }
  },
  mounted(){
    
    
  },
  methods:{
    init() {
      if (this.config.url && this.config.url !== '') {
        this.player = new Player(this.config);
        // 直播配置
        if(!this.isLive){
          this.player = new Player(this.config);
        }else{
          this.config.isLive = true
          // this.config.hls = {
          //   targetLatency: 10, // 直播目标延迟，默认 10 秒
          //   maxLatency:  20, // 直播允许的最大延迟，默认 20 秒
          //   disconnectTime: 0 // 直播断流时间，默认 0 秒，（独立使用时等于 maxLatency）
          // }
          if(document.createElement('video').canPlayType('application/vnd.apple.mpegurl')){
            this.player = new Player(this.config);
          }else{
            this.config.plugins = [HlsPlugin]
            this.player = new Player(this.config);
          }
          this.player.on(Events.ERROR, (error) => {
              this.playStatus = error.errorType ? error.errorType : ''
              console.log(error, this.playStatus, 'error>>>>????') // xgplayer 中的 Errors 对象
          })
          // const hls = this.player.plugins.hls.core
          // console.log(hls, 'hls>>>>????') // hls 上有很多有用的方法和属性
        }
        this.$emit('player', this.player)
      }
    },
    
  },
  beforeDestroy() {
    this.player && typeof this.player.destroy === 'function' && this.player.destroy();
  }
}
</script>
<style lang="scss" scoped>
// .in-player ::v-deep  .xgplayer-progress-played{
//   background-image: linear-gradient(270deg, #FDEC53 0%, #27FFEE 100%);
//   // background-image: linear-gradient(-90deg, #fa1f41, green);
// }
:deep .xgplayer .btn-text span{
  background: rgba(0, 0, 0, .0);
}
:deep .xgplayer.xgplayer-mobile xg-start-inner{
    background: rgba(0,0,0,.38) !important;
    border-radius: 50%
}
:deep .xgplayer .xgplayer-poster{
  background-color: #000
}
:deep .xgplayer .btn-text span{
  color: var(--background);
  font-size: 14px;
}
.player-wrap{
  position: relative;
}
.player-mask{
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 100;
  height: 100%;
  background: rgba(0, 0, 0, 1);
  
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.player-error-text{
  padding: 10px 15px;
  border-radius: 16px;
  color: var(--gray_05);
  background: rgba(255, 255, 255, 0.2);
}
</style>
