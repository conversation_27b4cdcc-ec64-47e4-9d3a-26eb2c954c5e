import { createRouter, createWebHistory } from "vue-router";
import i18n from '@/i18n/index'
const $t = i18n.global.t
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: "/home",
      component: () => import(/* webpackChunkName: "home" */ '@/views/home'),
      meta: {
        title: $t('router.home')
      }
    },
    {
      path: "/",
      component: () => import(/* webpackChunkName: "nav" */ '@/views/nav-menu'),
      meta: {
        title: $t('router.home')
      }
    },
    {
      path: "/investment",
      component: () => import(/* webpackChunkName: "investment" */ '@/views/investment'),
      meta: {
        title: $t('router.investment')
      }
    },
    {
      path: "/company",
      component: () => import(/* webpackChunkName: "strategy" */ '@/views/company'),
      meta: {
        title: $t('router.company')
      }
    },
    {
      path: "/pioneer",
      component: () => import(/* webpackChunkName: "pioneer" */ '@/views/pioneer'),
      meta: {
        title: $t('router.pioneer')
      }
    },
    {
      path: "/strategy",
      component: () => import(/* webpackChunkName: "pioneer" */ '@/views/strategy'),
      meta: {
        title: $t('router.strategy')
      }
    },
    {
      path: "/details",
      component: () => import(/* webpackChunkName: "strategy-details" */ '@/views/details'),
      meta: {
        title: ' '
      }
    },
    {
      path: "/video/detail",
      component: () => import(/* webpackChunkName: "video-details" */ '@/views/video/detail'),
      meta: {
        title: ' '
      }
    },
    {
      path: "/video/live",
      component: () => import(/* webpackChunkName: "live" */ '@/views/video/live'),
      meta: {
        title: ' '
      }
    },
  ]
});

export default router
