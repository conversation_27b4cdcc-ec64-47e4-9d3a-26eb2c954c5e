<template>
  <popup
    v-model="visible"
    position="right"
  >
    <div class="right-content">
      Popup Right
    </div>
  </popup>
</template>
<script>
import popup from "@/components/popup/index.vue";
export default {
  data() {
    return {
      moreLiveList: [],
      visible: false
    }
  },
  components: {
    popup
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:modelValue'],
  watch: {
    modelValue(val) {
      this.visible = val
    },
  },
  methods:{

  }
}
</script>
<style lang="scss" scoped>
.right-content{
  width: 200px;
  height: 100%;
  background: #fff;
}
</style>