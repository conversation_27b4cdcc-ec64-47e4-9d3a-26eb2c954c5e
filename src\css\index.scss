.van-tabs {
  .van-tabs__wrap {
    .van-tabs__nav {
      background: transparent;
      padding-bottom: 0;
      .van-tab--line {
        background: var(--background);
        max-height: 30px;
        color: var(--text_3rd);
        &.van-tab--active {
          background: var(--brand_01);
          color: var(--text_5th);
          font-weight: 500;
        }
      }
      .van-tab--grow {
        padding: 0 16px;
        background: var(--background);
        margin-right: 8px;
        border-radius: 20px;
        max-height: 30px;
        color: var(--text_3rd);
        &.van-tab--active {
          background: var(--brand_01);
          color: var(--text_5th);
          font-weight: 500;
        }
        &:last-child {
          margin-right: 0;
        }
      }
      .van-tabs__line {
        display: none;
      }
    }
  }
  .van-tabs__nav--line.van-tabs__nav--shrink, .van-tabs__nav--line.van-tabs__nav--complete {
    padding-left: 0;
    padding-right: 0;
  }
}
.van-list__finished-text{
  position: relative;
}
.van-list__finished-text::after{
  position: absolute;
  top: 15px;
  right: 0;
  content: "";
  display: block;
  width: 100px;
  border-bottom: 0.5px solid var(--line_01);
  margin: 10px 0;
}
.van-list__finished-text::before{
  position: absolute;
  top: 15px;
  left: 0;
  content: "";
  display: block;
  width: 100px;
  border-bottom: 0.5px solid var(--line_01);
  margin: 10px 0;
}