const path = require('path')
const { defineConfig } = require('@vue/cli-service');
let timeStamp = new Date().getTime();
module.exports = defineConfig({
  transpileDependencies: true,
  productionSourceMap: false,
  lintOnSave: false,
  devServer: {
    https: false,
    open: false,
    port: 3022,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
      "Access-Control-Allow-Headers": "X-Requested-With, content-type, Authorization"
    }
  },
  css: {
    loaderOptions: {
      sass: {
        additionalData: `@import "@/css/variable.scss";@import "@/css/main.scss";`,
      },
    },
    extract: { // 打包后css文件名称添加时间戳
      filename: `css/[name].${timeStamp}.css`,
      chunkFilename: `css/[name].${timeStamp}.css`,
    }
  },
})
