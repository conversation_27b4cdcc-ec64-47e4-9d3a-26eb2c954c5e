// import { Toast } from 'mand-mobile';
import { delParam } from './urlParams';
import { isIphone } from '@/utils/jsBridge/shared/index.js'
let CallApp  = null;

const yingyongbaoLink = 'https://www.eddidsfl.com/download?time=123';
const yingyongbaoLinkInter = 'https://www.eddid.com.hk/sf/download/';
const appstoreLink =  'https://itunes.apple.com/cn/app/艾德金融/id1448584180?l=zh&ls=1&mt=8';
const appstoreLinkInter =  'https://apps.apple.com/hk/app/艾德一站通eddid-one-港美股期貨基金證券交易開戶平臺/id1600014678?mt=8';

const goDownload = () => {
    const userAgent = window.navigator.userAgent;
    const isAndroid = /(Android)/i.test(userAgent); //android终端
    const isIOS = /(iPhone|iPad|iPod|iOS|Mac|Mac)/i.test(userAgent); //ios终端
    let inter = getUrlParam('inter')
    //android端
    if (isAndroid) {
        setTimeout(()=>{
            //应用宝下载地址
            if(inter === 'inter'){
              window.location.href = yingyongbaoLinkInter
            }else{
              window.location.href = yingyongbaoLink
            }

        }, 2000);
    }
    //ios端
    if (isIOS) {
        var loadDateTime = Date.now()
        setTimeout(function () {
            var timeOutDateTime = Date.now()
            if (timeOutDateTime - loadDateTime < 5000) {
                //App store下载地址
                if(inter === 'inter'){
                  window.location.href = appstoreLinkInter;
                }else{
                  window.location.href = appstoreLink
                }
            }
        }, 300);
    }
}
function getUrlParam(k) {
  let regExp = new RegExp(`([?]|&)${k}=([^&]*)(&|$)`);
  let STurl = window.sessionStorage.getItem('url');
  let url = STurl || window.location.href;
  let result = url.match(regExp);
  if (result) {
    return decodeURIComponent(result[2]);
  }
  return null;
}
// 已下载APP直接打开，否则引导下载
// type:
export const downloadEddidOne = (({ type= '', url= window.location.href }) => {
    let intPath = 'ios_springboard/intl/launchApp.html'
    let chinaPath = 'ios_springboard/china/launchApp.html'
    if(!CallApp) CallApp = require('callapp-lib');
    let inter = getUrlParam('inter')
    const options = {
        scheme: {
            protocol: inter === 'inter' ? 'eddidinternational' : 'eddidone',
        },
        intent: {
            scheme: inter === 'inter' ? 'eddidinternational' : 'eddidone',
            package: inter === 'inter' ? 'eddid.international' : 'io.newtype.eddid.app'
        },
        universal: {
          host: 'cdn-appstatic.eddidapp.com',
        },
        appstore: appstoreLink,
        yingyongbao: inter === 'inter' ? yingyongbaoLinkInter : yingyongbaoLink,
        timeout: 1500, // 默认两秒
        fallback:  null,
        // logFunc:()=>{},
    };
    if(!CallApp){
        console.error('CallApp未定义');
        return;
    }
    const callLib = new CallApp(options);
    let path = ''

    if(inter === 'inter' && isIphone){
      path = intPath
    }else if(isIphone){
      path = chinaPath
    }
    if(getUrlParam('needLogin') === 'true'){
      url += '&needLogin=true'
    }
    let paramsUrl = url;
    if(paramsUrl.indexOf('source=web')>0){
        paramsUrl = delParam({ url, paramKey: ['source','theme'] }) // 过滤source参数
        paramsUrl += `&source=app`;
    }
    callLib.open({
        param: {
            type,
            url: encodeURIComponent(paramsUrl)
        },
        path: path,
        callback:()=>{ // 失败回调
            goDownload();  // 没有自动唤端的话,证明手机里面没有app
        }
    });
});
